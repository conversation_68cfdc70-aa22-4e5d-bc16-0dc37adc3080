<div class="patient-list-container">
    <!-- Header Section -->
    <div class="header-section" [formGroup]="FilterForm">
        <!-- Search Bar -->
        <div class="search-container">
            <div class="search-input-wrapper">
                <img src="assets/icons/icon-MagnifyingGlass.svg" class="img-icon search-icon">
                <input type="text" class="search-input" placeholder="Search Name, Account, MRN, Room"
                    [(ngModel)]="searchByName" [ngModelOptions]="{standalone: true}" (input)="onKeyPatientSearch()">
            </div>
            <button class="sort-btn" (click)="toggleSideNav('sort')">
                <img src="assets/icons/icon-sort.svg" class="img-icon">
            </button>
            <button class="filter-btn">
                <img src="assets/icons/icon-filter-active.svg" class="img-icon">
            </button>
        </div>

        <!-- Sort Tabs -->
        <div class="filter-tabs mb-3" *ngIf="hideSideNav">
            <div class="facility-dropdown">
                <mat-label>Sort By</mat-label>
                <mat-select id="sortColumn" (selectionChange)="onChangeForSort($event)" class="form-control"
                    [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}" [value]="sortColumnBy">
                    <mat-option *ngFor="let sortOption of sortOptions"
                        [value]="sortOption.id">{{sortOption.name}}</mat-option>
                </mat-select>
            </div>
            <div class="facility-dropdown">
                <mat-label>Sort Order</mat-label>
                <mat-select id="sortOrder" (selectionChange)="onChangeForSortOrder($event)" class="form-control"
                    [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}" [value]="orderBy">
                    <mat-option *ngFor="let sortOrder of sortOrders" [value]="sortOrder">{{sortOrder}}</mat-option>
                </mat-select>
            </div>
        </div>

        <!-- Filter Tabs -->
        <div class="filter-tabs">
            <div class="facility-dropdown">
                <mat-select id="ddlFacility" formControlName="ddlFacility" (selectionChange)="FilterPatientDetails()"
                    class="form-control" [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}">
                    <mat-option [value]="''" disabled>Select Facility</mat-option>
                    <mat-option [value]="s.facilityName"
                        *ngFor="let s of listOfFacilities">{{s.facilityName}}</mat-option>
                </mat-select>
            </div>
            <div class="tabs">
                <button class="tab-btn active" [class.active]="activeTab === 'myPatients'"
                    (click)="setActiveTab('myPatients')">
                    My Patients
                </button>
                <button class="tab-btn" [class.active]="activeTab === 'hidden'" (click)="setActiveTab('hidden')">
                    Hidden
                </button>
            </div>
        </div>
    </div>

    <!-- Patient Cards Starts -->
    <app-m-patients [listOfPatients]="listOfPatients" [p]="p" [pageName]="'/m/physician/my-patients'"
        [FilterForm]="FilterForm" [totalCount]="messageCount" [searchByName]="searchByName" [device]="device"
        (enventUpdateSortObj)="updateSortObj($event)" [orderBy]="orderBy" [sortColumnBy]="sortColumnBy"
        (eventListOfPatients)="childListOfPatients($event)"
        (eventRemovePatient)="RemovePatient($event)"></app-m-patients>
    <!-- Patient Cards Ends -->

</div>