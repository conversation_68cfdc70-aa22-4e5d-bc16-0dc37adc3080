import { Component, Input } from '@angular/core';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { CommonService } from 'src/app/services/common/common.service';
import { MStartNewEncounterComponent } from './m-start-new-encounter.component';
import { MatTabChangeEvent } from '@angular/material/tabs';

declare let $: any;

@Component({
  selector: 'app-m-icddata',
  templateUrl: './m-icddata.component.html',
  styleUrls: ['./m-physician.scss']
})
export class MICDDataComponent  {
  @Input() lisfOfICDData: Array<any> = [];
  public chkValuesArray: Array<any> = [];
  public editedValue: string;
  public searchICDs: string;
  public request: any = {};
  public lisfOfSearchICDsData: Array<any> = [];
  public filterICDs: string;
  public isSearch: boolean = false;
  public timeout: any = null;
  constructor(private readonly encrDecr: EncrDecrServiceService, private readonly commonServ: CommonService, private readonly startnewEnvCmp: MStartNewEncounterComponent) { }
  public isICDchange: string = "false";

  search() {
    this.lisfOfSearchICDsData = [];
    this.filterICDs = "";
    this.isSearch = true;
  }

  fav() {
    this.isSearch = false;
    this.startnewEnvCmp.getICDData();
  }

  searchICDData() {
    clearTimeout(this.timeout);

    this.timeout = setTimeout(() => {
      this.commonServ.startLoading();
      this.request.ICDNAME = this.encrDecr.set(this.filterICDs);
      this.request.PHYSICIANMAILID = this.encrDecr.set('PHYSICIAN');
      this.commonServ.searchICDData(this.request).subscribe((p: any) => {
        this.lisfOfSearchICDsData = p;
        this.request = {};
        this.commonServ.stopLoading();
      }, error => {
        this.request = {};
        console.error(error.status);
      });
    }, 2000);
  }

  chkChangeEvent(event) {
    for (let item of this.lisfOfICDData) {
      if (item.icD_ID == event.source.id) {
        item.checked = event.source.checked;
      }
    }
  }

  chkChangeEventSearch(event) {
    for (let item of this.lisfOfSearchICDsData) {
      if ('sch-' + item.icD_ID == event.source.id) {
        item.checked = event.source.checked;
      }
    }
  }

  addICDData(lisfOfICDData) {
    this.commonServ.startLoading();
    lisfOfICDData.forEach(x => {
      if (this.startnewEnvCmp.testData.listofTestICDS == null && x.checked) {
        this.startnewEnvCmp.testData.listofTestICDS = [];
        this.startnewEnvCmp.testData.listofTestICDS.push(x.icdname);
      }
      else if (this.startnewEnvCmp.testData.listofTestICDS && !this.startnewEnvCmp.testData.listofTestICDS.includes(x.icdname) && x.checked) {
        this.startnewEnvCmp.testData.listofTestICDS.push(x.icdname);
      }
    });
    this.commonServ.stopLoading();
    this.startnewEnvCmp.closeICDCodesPopup();
  }


  addICDDataSearch(lisfOfSearchICDsData) {
    this.commonServ.startLoading();
    lisfOfSearchICDsData.forEach(x => {
      if (this.startnewEnvCmp.testData.listofTestICDS == null && x.checked) {
        this.startnewEnvCmp.testData.listofTestICDS = [];
        this.startnewEnvCmp.testData.listofTestICDS.push(x.icdname);
      }
      else if (this.startnewEnvCmp.testData.listofTestICDS && !this.startnewEnvCmp.testData.listofTestICDS.includes(x.icdname) && x.checked) {
        this.startnewEnvCmp.testData.listofTestICDS.push(x.icdname);
      }
    });
    this.commonServ.stopLoading();
    this.startnewEnvCmp.closeICDCodesPopup();
  }

  favUnfavICDCodesAdd(status, item) {
    let confirmMessage = 'Do you want to ' + (status == 0 ? 'unfavorite' : 'favorite') + ' this ICD Code?';
    if (confirm(confirmMessage)) {
      this.commonServ.startLoading();
      this.request.PHYSICIANMAILID = this.encrDecr.set('PHYSICIAN');
      this.request.STATUS = status;
      this.request.ICD_ID = item.icD_ID;
      this.commonServ.insertICDData(this.request).subscribe((p: any) => {
        this.request = {};
        if (p > 0) {
          item.status = status;
        }
        this.commonServ.stopLoading();
      }, error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });
    }
  }

  onMatGroupTabClick(event: MatTabChangeEvent): void {
      if (event.tab.textLabel == 'Favorites') {
        this.fav();
      }
      else{
        this.search()
      }
    }


}
