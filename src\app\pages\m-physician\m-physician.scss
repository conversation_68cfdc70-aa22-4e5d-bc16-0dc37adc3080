@import url('https://fonts.googleapis.com/css2?family=Ubuntu:wght@100;200;300;400;500;600;700;800;900&display=swap');

// Set default font size and family for this module
:host,
.m-physician-root {
    font-size: 14px;
    // font-family: 'Ubuntu', sans-serif;
    font-weight: 400;
    color: #1E293B;
    // line-height: 1.75;
    letter-spacing: 0.7px;
}

.patient-list-container {
    padding: 8px;
    background-color: #FFF;
    color: var(--text-text-primary, #1E293B);
    border: 1px solid #ddd;
}

.header-section {
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #ddd;
}

.search-container {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
}

.search-input-wrapper {
    flex: 1;
    position: relative;
}

.search-icon {
    height: 18px;
    width: 18px;
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
}

.search-input {
    width: 100%;
    padding: 12px 12px 12px 32px;
    border-radius: var(--Radius-M, 4px);
    border: 1px solid var(--border-border-default, #D5DBE1);
    background: var(--background-background-default, #FFF);
    color: var(--gray-600, #757575);
}

.sort-btn,
.filter-btn {
    padding: 8px;
    border-radius: 4px;
    border: 1px solid var(--border-border-default, #D5DBE1);
    background: var(--background-background-default, #FFF);
}

.sort-btn:hover,
.filter-btn:hover {
    background: rgba(141, 141, 141, 0.12);
}

.filter-tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.facility-dropdown {
    flex: 1;
    margin-right: 8px;
}

.tabs {
    display: flex;
    gap: 4px;
    align-items: center;
    padding: 0.25rem;
    background-color: #E2E8F0;
    border-radius: 0.25rem;
}

.tab-btn {
    padding: 6px 12px;
    border: none;
    background: transparent;
    font-size: 14px;
    border-radius: 4px;
    cursor: pointer;
}

.tab-btn.active {
    background: white;
}

.tab-btn:hover:not(.active) {
    background: #e9ecef;
}

.patient-cards {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.patient-card {
    background: #FFF;
    border-radius: 6px;
    // box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid #D5DBE1;
}

.action-buttons {
    display: flex;
    justify-content: space-between;
    gap: 6px;
    margin-top: 16px;
    padding-top: 12px;
    border-top: 1px solid #BDBDBD;
}

.action-btn {
    min-width: calc(100% / 5);
    width: 100%;
    min-height: 40px;
    border: none;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn:hover {
    transform: scale(1.05);
}

.menu-btn {
    min-width: 10%;
    width: auto;
}

.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.mobile-text-lg {
    font-size: 1rem;
}

.font-medium {
    font-weight: 500;
}

.h-6 {
    height: 1.5rem !important;
}

.h-20 {
    height: 5rem !important;
}

.border-l-2 {
    border-left: 2px solid #BDBDBD !important;
}

.h-1 {
    height: 0.25rem !important;
}

.w-12 {
    width: 3rem !important;
}

.border-t-2 {
    border-top: 2px solid #BDBDBD !important;
}

.gap-x-4 {
    column-gap: 1rem !important;
}

.grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
}

.grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr)) !important;
}

.grid {
    display: grid !important;
}

.items-center {
    align-items: center !important;
}

.h-4 {
    height: 1rem !important;
}

.mt-auto {
    margin-top: auto !important;
}

.mt-8 {
    margin-top: 2rem !important;
}

.bg-gray-50 {
    background-color: #F8FAFC !important;
}

.bg-white {
    background: white !important;
}

.border {
    border: 1px solid #ddd !important;
}

.rounded-md {
    border-radius: 0.375rem !important;
}

.px-6 {
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
}

.ml-2 {
    margin-left: 0.5rem !important;
}

.pl-6 {
    margin-left: 1.5rem !important;
}

.mobile-p-3 {
    padding: 0.75rem !important;
}

.mobile-p-4 {
    padding: 1rem !important;
}

.border-b {
    border-bottom: 1px solid #ddd;
}

button:focus {
    outline: 1px solid #ddd;
}

.max-h-70 {
    max-height: 70vh;
    overflow-y: auto;
}

.max-h-80 {
    max-height: 80vh;
    overflow-y: auto;
    padding: 1.5rem;
}

.min-h-80 {
    min-height: 80vh;
}

.max-h-90 {
    max-height: 90vh;
    overflow-y: auto;
    padding: 1.5rem;
}

.cursor-pointer {
    cursor: pointer;
}

.justify-between {
    justify-content: space-between;
}

.justify-center {
    justify-content: center;
}

.h-50px {
    height: 50px;
}

.w-50px {
    width: 50px;
}

.h-40px {
    height: 40px;
}

.w-40px {
    width: 40px;
}

.overflow-hidden {
    overflow: hidden;
}

.bg-primary-100 {
    background: #e0e7ff;
}

.text-secondary {
    color: #64748B !important;
}

.text-secondary2 {
    color: #616161 !important;
}

.text-sm {
    font-size: 0.8rem !important;
}

.close-btn {
    background: none;
    border: none;
}

.text-link {
    color: #1E40AF;
    font-weight: 500;
}

.w-full {
    width: 100% !important;
}

// Actions popup css start

.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(40, 40, 40, 0.35);
    z-index: 999 !important;
    pointer-events: all;
}

.actions-popup {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background: #f7f7f7;
    box-shadow: 0 -2px 16px rgba(0, 0, 0, 0.15);
    border-radius: 16px 16px 0 0;
    animation: slideUp 250ms ease-out;
    margin: 0 auto;
    display: block;
    z-index: 1000;

    .actions-header {
        font-size: 1.1rem;
        font-weight: 500;
        display: flex;
        justify-content: space-between;
        padding: 1.5rem;

        .actions-subtitle {
            font-size: 0.9rem;
            font-weight: 400;
        }

        .close-btn {
            background: none;
            border: none;
        }
    }

    .actions-card {
        background-color: #fff;
        display: flex;
        flex-direction: column;
        box-shadow: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .1) !important;
        border-radius: 10px;

        .action-item {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
        }

        .action-item:hover {
            background-color: #ddd;
        }
    }

    .actions-list {
        margin-top: 16px;
        display: flex;
        flex-direction: column;
        gap: 8px;
        padding: 0 24px;

        .actions-item {
            background: #f7f7f7;
            border: none;
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;

            .action-icon {
                width: 22px;
                height: 22px;
            }
        }
    }
}

/* ############# New Encounter CSS start ############## */

.details-header {
    background-color: #fff;
    padding: 12px 8px;
    display: flex;
    align-items: center;

    .back-btn {
        background: none;
        border: none;
        cursor: pointer;
        margin-right: 1rem;
    }

    .patient-name {
        font-size: 1rem;
        font-weight: 500;
    }
}

.mark-seen-btn {
    background-color: #fff;
    border: 1px solid #006A94;
    color: #005F85;
    padding: 12px;
    border-radius: 2px;
    width: 100%;
    font-weight: 500;
}

.details-buttons {
    display: flex;
    gap: 16px;

    .details-btn {
        flex: 1;
        padding: 8px;
        border: 1px solid #D5DBE1;
        background-color: #fff;
        border-radius: 2px;
    }

}

.code-content {
    background-color: #FFF;
    margin: 10px 0;
    padding: 1rem;

    .code-section {
        margin-bottom: 1rem;
        background-color: #fff;
        overflow: hidden;

        .code-item {
            display: flex;
            align-items: center;
            border: 1px solid #D5DBE1;
            border-radius: 6px;
            cursor: pointer;

            .code-icon {
                padding: 16px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 12px;
            }

            .code-desc {
                display: flex;
                padding: 16px;
                width: 100%;
                align-items: center;
            }

            .cpt-icon {
                background-color: #E6F0F4;
            }

            .icd-icon {
                background-color: #FFF1F2;
            }

            .chevron {
                margin-left: auto;
            }
        }
    }

    .patient-card {
        margin-bottom: 1rem;
    }
}

// Attachments start


.attachment-list {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .attachment-item {
        border-bottom: 1px solid #eee;
        padding-bottom: 8px;

        .attachment-actions {
            display: flex;
            gap: 6px;

            .action-btn {
                background: none;
                border: none;
                padding: 0;
                cursor: pointer;
            }
        }
    }
}

.upload-section {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .file-upload-label {
        display: block;
        width: 100%;
        background: #f7f7f7;
        border-radius: 8px;
        border: 1px dashed #bbb;
        text-align: center;
        padding: 12px 0;
        cursor: pointer;

        .file-upload-text {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            color: #888;
            font-size: 15px;
        }
    }

    .upload-btn {
        width: 100%;
        background: #0077a3;
        color: #fff;
        border: none;
        border-radius: 6px;
        padding: 12px 0;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        margin-top: 2px;
    }

    .cancel-btn {
        width: 100%;
        background: #FFF;
        color: red;
        border: 1px solid red;
        border-radius: 6px;
        padding: 12px 0;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        margin-top: 2px;
    }

    .add-btn {
        width: 100%;
        background: #FFF;
        color: #005F85;
        border: 1px solid #006A94;
        border-radius: 4px;
        padding: 12px 0;
        font-weight: 500;
        margin-top: 2px;
    }
}

.upload-footer {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    background: #fff;
    box-shadow: 0 0 6px #D5DBE1;
    padding: 16px 16px 12px 16px;
    z-index: 1100;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 480px;
    margin: 0 auto;
}

.upload-footer2 {
    background: #fff;
    box-shadow: 0 0 6px #D5DBE1;
    padding: 16px 16px 12px 16px;
    z-index: 1100;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 480px;
    margin: 0 auto;
}

.mdc-text-field {
    border: 1px solid #ddd !important;
    border-radius: 6px !important;
}
.mb-8{
    margin-bottom: 4rem !important;
}